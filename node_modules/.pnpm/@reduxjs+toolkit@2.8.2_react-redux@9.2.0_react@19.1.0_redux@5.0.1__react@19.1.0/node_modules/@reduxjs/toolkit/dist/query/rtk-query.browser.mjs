var Ue=(y=>(y.uninitialized="uninitialized",y.pending="pending",y.fulfilled="fulfilled",y.rejected="rejected",y))(Ue||{});function Le(e){return{status:e,isUninitialized:e==="uninitialized",isLoading:e==="pending",isSuccess:e==="fulfilled",isError:e==="rejected"}}import{createAction as X,createSlice as ae,createSelector as Ye,createAsyncThunk as je,combineReducers as Xe,createNextState as Se,isAnyOf as oe,isAllOf as Pe,isAction as Ze,isPending as Ie,isRejected as fe,isFulfilled as W,isRejectedWithValue as me,isAsyncThunkAction as He,prepareAutoBatched as ge,SHOULD_AUTOBATCH as ke,isPlainObject as te,nanoid as Be}from"@reduxjs/toolkit";var et=te;function Me(e,n){if(e===n||!(et(e)&&et(n)||Array.isArray(e)&&Array.isArray(n)))return n;let d=Object.keys(n),g=Object.keys(e),y=d.length===g.length,x=Array.isArray(n)?[]:{};for(let T of d)x[T]=Me(e[T],n[T]),y&&(y=e[T]===x[T]);return y?e:x}function J(e){let n=0;for(let d in e)n++;return n}var _e=e=>[].concat(...e);function tt(e){return new RegExp("(^|:)//").test(e)}function nt(){return typeof document>"u"?!0:document.visibilityState!=="hidden"}function se(e){return e!=null}function rt(){return typeof navigator>"u"||navigator.onLine===void 0?!0:navigator.onLine}var Ct=e=>e.replace(/\/$/,""),Ft=e=>e.replace(/^\//,"");function it(e,n){if(!e)return n;if(!n)return e;if(tt(n))return n;let d=e.endsWith("/")||!n.startsWith("?")?"/":"";return e=Ct(e),n=Ft(n),`${e}${d}${n}`}function at(e,n,d){return e.has(n)?e.get(n):e.set(n,d).get(n)}var ot=(...e)=>fetch(...e),vt=e=>e.status>=200&&e.status<=299,Ot=e=>/ion\/(vnd\.api\+)?json/.test(e.get("content-type")||"");function st(e){if(!te(e))return e;let n={...e};for(let[d,g]of Object.entries(n))g===void 0&&delete n[d];return n}function Nt({baseUrl:e,prepareHeaders:n=h=>h,fetchFn:d=ot,paramsSerializer:g,isJsonContentType:y=Ot,jsonContentType:x="application/json",jsonReplacer:T,timeout:k,responseHandler:w,validateStatus:A,...P}={}){return typeof fetch>"u"&&d===ot&&console.warn("Warning: `fetch` is not available. Please supply a custom `fetchFn` property to use `fetchBaseQuery` on SSR environments."),async(R,a,f)=>{let{getState:B,extra:p,endpoint:o,forced:S,type:c}=a,u,{url:m,headers:D=new Headers(P.headers),params:b=void 0,responseHandler:I=w??"json",validateStatus:Q=A??vt,timeout:s=k,...t}=typeof R=="string"?{url:R}:R,r,i=a.signal;s&&(r=new AbortController,a.signal.addEventListener("abort",r.abort),i=r.signal);let l={...P,signal:i,...t};D=new Headers(st(D)),l.headers=await n(D,{getState:B,arg:R,extra:p,endpoint:o,forced:S,type:c,extraOptions:f})||D;let E=N=>typeof N=="object"&&(te(N)||Array.isArray(N)||typeof N.toJSON=="function");if(!l.headers.has("content-type")&&E(l.body)&&l.headers.set("content-type",x),E(l.body)&&y(l.headers)&&(l.body=JSON.stringify(l.body,T)),b){let N=~m.indexOf("?")?"&":"?",F=g?g(b):new URLSearchParams(st(b));m+=N+F}m=it(e,m);let v=new Request(m,l);u={request:new Request(m,l)};let C,M=!1,K=r&&setTimeout(()=>{M=!0,r.abort()},s);try{C=await d(v)}catch(N){return{error:{status:M?"TIMEOUT_ERROR":"FETCH_ERROR",error:String(N)},meta:u}}finally{K&&clearTimeout(K),r?.signal.removeEventListener("abort",r.abort)}let j=C.clone();u.response=j;let z,L="";try{let N;if(await Promise.all([h(C,I).then(F=>z=F,F=>N=F),j.text().then(F=>L=F,()=>{})]),N)throw N}catch(N){return{error:{status:"PARSING_ERROR",originalStatus:C.status,data:L,error:String(N)},meta:u}}return Q(C,z)?{data:z,meta:u}:{error:{status:C.status,data:z},meta:u}};async function h(R,a){if(typeof a=="function")return a(R);if(a==="content-type"&&(a=y(R.headers)?"json":"text"),a==="json"){let f=await R.text();return f.length?JSON.parse(f):null}return R.text()}}var G=class{constructor(n,d=void 0){this.value=n;this.meta=d}};async function qt(e=0,n=5){let d=Math.min(e,n),g=~~((Math.random()+.4)*(300<<d));await new Promise(y=>setTimeout(x=>y(x),g))}function Kt(e,n){throw Object.assign(new G({error:e,meta:n}),{throwImmediately:!0})}var ut={},Ut=(e,n)=>async(d,g,y)=>{let x=[5,(n||ut).maxRetries,(y||ut).maxRetries].filter(P=>P!==void 0),[T]=x.slice(-1),w={maxRetries:T,backoff:qt,retryCondition:(P,h,{attempt:R})=>R<=T,...n,...y},A=0;for(;;)try{let P=await e(d,g,y);if(P.error)throw new G(P);return P}catch(P){if(A++,P.throwImmediately){if(P instanceof G)return P.value;throw P}if(P instanceof G&&!w.retryCondition(P.value.error,d,{attempt:A,baseQueryApi:g,extraOptions:y}))return P.value;await w.backoff(A,w.maxRetries)}},Lt=Object.assign(Ut,{fail:Kt});var ne=X("__rtkq/focused"),Qe=X("__rtkq/unfocused"),re=X("__rtkq/online"),Te=X("__rtkq/offline"),Ve=!1;function jt(e,n){function d(){let g=()=>e(ne()),y=()=>e(Qe()),x=()=>e(re()),T=()=>e(Te()),k=()=>{window.document.visibilityState==="visible"?g():y()};return Ve||typeof window<"u"&&window.addEventListener&&(window.addEventListener("visibilitychange",k,!1),window.addEventListener("focus",g,!1),window.addEventListener("online",x,!1),window.addEventListener("offline",T,!1),Ve=!0),()=>{window.removeEventListener("focus",g),window.removeEventListener("visibilitychange",k),window.removeEventListener("online",x),window.removeEventListener("offline",T),Ve=!1}}return n?n(e,{onFocus:ne,onFocusLost:Qe,onOffline:Te,onOnline:re}):d()}function ue(e){return e.type==="query"}function yt(e){return e.type==="mutation"}function ye(e){return e.type==="infinitequery"}function he(e){return ue(e)||ye(e)}function xe(e,n,d,g,y,x){return Ht(e)?e(n,d,g,y).filter(se).map(we).map(x):Array.isArray(e)?e.map(we).map(x):[]}function Ht(e){return typeof e=="function"}function we(e){return typeof e=="string"?{type:e}:e}import{isDraftable as Vt,produceWithPatches as zt}from"immer";import"@reduxjs/toolkit";function dt(e,n){return e.catch(n)}var Ae=Symbol("forceQueryFn"),De=e=>typeof e[Ae]=="function";function pt({serializeQueryArgs:e,queryThunk:n,infiniteQueryThunk:d,mutationThunk:g,api:y,context:x}){let T=new Map,k=new Map,{unsubscribeQueryResult:w,removeMutationResult:A,updateSubscriptionOptions:P}=y.internalActions;return{buildInitiateQuery:o,buildInitiateInfiniteQuery:S,buildInitiateMutation:c,getRunningQueryThunk:h,getRunningMutationThunk:R,getRunningQueriesThunk:a,getRunningMutationsThunk:f};function h(u,m){return D=>{let b=x.endpointDefinitions[u],I=e({queryArgs:m,endpointDefinition:b,endpointName:u});return T.get(D)?.[I]}}function R(u,m){return D=>k.get(D)?.[m]}function a(){return u=>Object.values(T.get(u)||{}).filter(se)}function f(){return u=>Object.values(k.get(u)||{}).filter(se)}function B(u){}function p(u,m){let D=(b,{subscribe:I=!0,forceRefetch:Q,subscriptionOptions:s,[Ae]:t,...r}={})=>(i,l)=>{let E=e({queryArgs:b,endpointDefinition:m,endpointName:u}),v,O={...r,type:"query",subscribe:I,forceRefetch:Q,subscriptionOptions:s,endpointName:u,originalArgs:b,queryCacheKey:E,[Ae]:t};if(ue(m))v=n(O);else{let{direction:H,initialPageParam:q}=r;v=d({...O,direction:H,initialPageParam:q})}let C=y.endpoints[u].select(b),M=i(v),K=C(l());let{requestId:j,abort:z}=M,L=K.requestId!==j,N=T.get(i)?.[E],F=()=>C(l()),_=Object.assign(t?M.then(F):L&&!N?Promise.resolve(K):Promise.all([N,M]).then(F),{arg:b,requestId:j,subscriptionOptions:s,queryCacheKey:E,abort:z,async unwrap(){let H=await _;if(H.isError)throw H.error;return H.data},refetch:()=>i(D(b,{subscribe:!1,forceRefetch:!0})),unsubscribe(){I&&i(w({queryCacheKey:E,requestId:j}))},updateSubscriptionOptions(H){_.subscriptionOptions=H,i(P({endpointName:u,requestId:j,queryCacheKey:E,options:H}))}});if(!N&&!L&&!t){let H=at(T,i,{});H[E]=_,_.then(()=>{delete H[E],J(H)||T.delete(i)})}return _};return D}function o(u,m){return p(u,m)}function S(u,m){return p(u,m)}function c(u){return(m,{track:D=!0,fixedCacheKey:b}={})=>(I,Q)=>{let s=g({type:"mutation",endpointName:u,originalArgs:m,track:D,fixedCacheKey:b}),t=I(s);let{requestId:r,abort:i,unwrap:l}=t,E=dt(t.unwrap().then(M=>({data:M})),M=>({error:M})),v=()=>{I(A({requestId:r,fixedCacheKey:b}))},O=Object.assign(E,{arg:t.arg,requestId:r,abort:i,unwrap:l,reset:v}),C=k.get(I)||{};return k.set(I,C),C[r]=O,O.then(()=>{delete C[r],J(C)||k.delete(I)}),b&&(C[b]=O,O.then(()=>{C[b]===O&&(delete C[b],J(C)||k.delete(I))})),O}}}import{SchemaError as _t}from"@standard-schema/utils";var Re=class extends _t{constructor(d,g,y,x){super(d);this.value=g;this.schemaName=y;this._bqMeta=x}};async function ie(e,n,d,g){let y=await e["~standard"].validate(n);if(y.issues)throw new Re(y.issues,n,d,g);return y.value}function Wt(e){return e}var be=(e={})=>({...e,[ke]:!0});function ct({reducerPath:e,baseQuery:n,context:{endpointDefinitions:d},serializeQueryArgs:g,api:y,assertTagType:x,selectors:T,onSchemaFailure:k,catchSchemaFailure:w,skipSchemaValidation:A}){let P=(t,r,i,l)=>(E,v)=>{let O=d[t],C=g({queryArgs:r,endpointDefinition:O,endpointName:t});if(E(y.internalActions.queryResultPatched({queryCacheKey:C,patches:i})),!l)return;let M=y.endpoints[t].select(r)(v()),K=xe(O.providesTags,M.data,void 0,r,{},x);E(y.internalActions.updateProvidedBy([{queryCacheKey:C,providedTags:K}]))};function h(t,r,i=0){let l=[r,...t];return i&&l.length>i?l.slice(0,-1):l}function R(t,r,i=0){let l=[...t,r];return i&&l.length>i?l.slice(1):l}let a=(t,r,i,l=!0)=>(E,v)=>{let C=y.endpoints[t].select(r)(v()),M={patches:[],inversePatches:[],undo:()=>E(y.util.patchQueryData(t,r,M.inversePatches,l))};if(C.status==="uninitialized")return M;let K;if("data"in C)if(Vt(C.data)){let[j,z,L]=zt(C.data,i);M.patches.push(...z),M.inversePatches.push(...L),K=j}else K=i(C.data),M.patches.push({op:"replace",path:[],value:K}),M.inversePatches.push({op:"replace",path:[],value:C.data});return M.patches.length===0||E(y.util.patchQueryData(t,r,M.patches,l)),M},f=(t,r,i)=>l=>l(y.endpoints[t].initiate(r,{subscribe:!1,forceRefetch:!0,[Ae]:()=>({data:i})})),B=(t,r)=>t.query&&t[r]?t[r]:Wt,p=async(t,{signal:r,abort:i,rejectWithValue:l,fulfillWithValue:E,dispatch:v,getState:O,extra:C})=>{let M=d[t.endpointName],{metaSchema:K,skipSchemaValidation:j=A}=M;try{let z=B(M,"transformResponse"),L={signal:r,abort:i,dispatch:v,getState:O,extra:C,endpoint:t.endpointName,type:t.type,forced:t.type==="query"?o(t,O()):void 0,queryCacheKey:t.type==="query"?t.queryCacheKey:void 0},N=t.type==="query"?t[Ae]:void 0,F,_=async(q,U,V,pe)=>{if(U==null&&q.pages.length)return Promise.resolve({data:q});let Z={queryArg:t.originalArgs,pageParam:U},ce=await H(Z),$=pe?h:R;return{data:{pages:$(q.pages,ce.data,V),pageParams:$(q.pageParams,U,V)},meta:ce.meta}};async function H(q){let U,{extraOptions:V,argSchema:pe,rawResponseSchema:Z,responseSchema:ce}=M;if(pe&&!j&&(q=await ie(pe,q,"argSchema",{})),N?U=N():M.query?U=await n(M.query(q),L,V):U=await M.queryFn(q,L,V,le=>n(le,L,V)),U.error)throw new G(U.error,U.meta);let{data:$}=U;Z&&!j&&($=await ie(Z,U.data,"rawResponseSchema",U.meta));let ee=await z($,U.meta,q);return ce&&!j&&(ee=await ie(ce,ee,"responseSchema",U.meta)),{...U,data:ee}}if(t.type==="query"&&"infiniteQueryOptions"in M){let{infiniteQueryOptions:q}=M,{maxPages:U=1/0}=q,V,pe={pages:[],pageParams:[]},Z=T.selectQueryEntry(O(),t.queryCacheKey)?.data,$=o(t,O())&&!t.direction||!Z?pe:Z;if("direction"in t&&t.direction&&$.pages.length){let ee=t.direction==="backward",Ee=(ee?ze:Ce)(q,$,t.originalArgs);V=await _($,Ee,U,ee)}else{let{initialPageParam:ee=q.initialPageParam}=t,le=Z?.pageParams??[],Ee=le[0]??ee,Je=le.length;V=await _($,Ee,U),N&&(V={data:V.data.pages[0]});for(let Ge=1;Ge<Je;Ge++){let wt=Ce(q,V.data,t.originalArgs);V=await _(V.data,wt,U)}}F=V}else F=await H(t.originalArgs);return K&&!j&&F.meta&&(F.meta=await ie(K,F.meta,"metaSchema",F.meta)),E(F.data,be({fulfilledTimeStamp:Date.now(),baseQueryMeta:F.meta}))}catch(z){let L=z;if(L instanceof G){let N=B(M,"transformErrorResponse"),{rawErrorResponseSchema:F,errorResponseSchema:_}=M,{value:H,meta:q}=L;try{F&&!j&&(H=await ie(F,H,"rawErrorResponseSchema",q)),K&&!j&&(q=await ie(K,q,"metaSchema",q));let U=await N(H,q,t.originalArgs);return _&&!j&&(U=await ie(_,U,"errorResponseSchema",q)),l(U,be({baseQueryMeta:q}))}catch(U){L=U}}try{if(L instanceof Re){let N={endpoint:t.endpointName,arg:t.originalArgs,type:t.type,queryCacheKey:t.type==="query"?t.queryCacheKey:void 0};M.onSchemaFailure?.(L,N),k?.(L,N);let{catchSchemaFailure:F=w}=M;if(F)return l(F(L,N),be({baseQueryMeta:L._bqMeta}))}}catch(N){L=N}throw console.error(L),L}};function o(t,r){let i=T.selectQueryEntry(r,t.queryCacheKey),l=T.selectConfig(r).refetchOnMountOrArgChange,E=i?.fulfilledTimeStamp,v=t.forceRefetch??(t.subscribe&&l);return v?v===!0||(Number(new Date)-Number(E))/1e3>=v:!1}let S=()=>je(`${e}/executeQuery`,p,{getPendingMeta({arg:r}){let i=d[r.endpointName];return be({startedTimeStamp:Date.now(),...ye(i)?{direction:r.direction}:{}})},condition(r,{getState:i}){let l=i(),E=T.selectQueryEntry(l,r.queryCacheKey),v=E?.fulfilledTimeStamp,O=r.originalArgs,C=E?.originalArgs,M=d[r.endpointName],K=r.direction;return De(r)?!0:E?.status==="pending"?!1:o(r,l)||ue(M)&&M?.forceRefetch?.({currentArg:O,previousArg:C,endpointState:E,state:l})?!0:!(v&&!K)},dispatchConditionRejection:!0}),c=S(),u=S(),m=je(`${e}/executeMutation`,p,{getPendingMeta(){return be({startedTimeStamp:Date.now()})}}),D=t=>"force"in t,b=t=>"ifOlderThan"in t,I=(t,r,i)=>(l,E)=>{let v=D(i)&&i.force,O=b(i)&&i.ifOlderThan,C=(K=!0)=>{let j={forceRefetch:K,isPrefetch:!0};return y.endpoints[t].initiate(r,j)},M=y.endpoints[t].select(r)(E());if(v)l(C());else if(O){let K=M?.fulfilledTimeStamp;if(!K){l(C());return}(Number(new Date)-Number(new Date(K)))/1e3>=O&&l(C())}else l(C(!1))};function Q(t){return r=>r?.meta?.arg?.endpointName===t}function s(t,r){return{matchPending:Pe(Ie(t),Q(r)),matchFulfilled:Pe(W(t),Q(r)),matchRejected:Pe(fe(t),Q(r))}}return{queryThunk:c,mutationThunk:m,infiniteQueryThunk:u,prefetch:I,updateQueryData:a,upsertQueryData:f,patchQueryData:P,buildMatchThunkActions:s}}function Ce(e,{pages:n,pageParams:d},g){let y=n.length-1;return e.getNextPageParam(n[y],n,d[y],d,g)}function ze(e,{pages:n,pageParams:d},g){return e.getPreviousPageParam?.(n[0],n,d[0],d,g)}function Fe(e,n,d,g){return xe(d[e.meta.arg.endpointName][n],W(e)?e.payload:void 0,me(e)?e.payload:void 0,e.meta.arg.originalArgs,"baseQueryMeta"in e.meta?e.meta.baseQueryMeta:void 0,g)}import{isDraft as $t}from"immer";import{applyPatches as lt,original as Jt}from"immer";function ve(e,n,d){let g=e[n];g&&d(g)}function de(e){return("arg"in e?e.arg.fixedCacheKey:e.fixedCacheKey)??e.requestId}function ft(e,n,d){let g=e[de(n)];g&&d(g)}var Oe={};function mt({reducerPath:e,queryThunk:n,mutationThunk:d,serializeQueryArgs:g,context:{endpointDefinitions:y,apiUid:x,extractRehydrationInfo:T,hasRehydrationInfo:k},assertTagType:w,config:A}){let P=X(`${e}/resetApiState`);function h(Q,s,t,r){Q[s.queryCacheKey]??={status:"uninitialized",endpointName:s.endpointName},ve(Q,s.queryCacheKey,i=>{i.status="pending",i.requestId=t&&i.requestId?i.requestId:r.requestId,s.originalArgs!==void 0&&(i.originalArgs=s.originalArgs),i.startedTimeStamp=r.startedTimeStamp;let l=y[r.arg.endpointName];ye(l)&&"direction"in s&&(i.direction=s.direction)})}function R(Q,s,t,r){ve(Q,s.arg.queryCacheKey,i=>{if(i.requestId!==s.requestId&&!r)return;let{merge:l}=y[s.arg.endpointName];if(i.status="fulfilled",l)if(i.data!==void 0){let{fulfilledTimeStamp:E,arg:v,baseQueryMeta:O,requestId:C}=s,M=Se(i.data,K=>l(K,t,{arg:v.originalArgs,baseQueryMeta:O,fulfilledTimeStamp:E,requestId:C}));i.data=M}else i.data=t;else i.data=y[s.arg.endpointName].structuralSharing??!0?Me($t(i.data)?Jt(i.data):i.data,t):t;delete i.error,i.fulfilledTimeStamp=s.fulfilledTimeStamp})}let a=ae({name:`${e}/queries`,initialState:Oe,reducers:{removeQueryResult:{reducer(Q,{payload:{queryCacheKey:s}}){delete Q[s]},prepare:ge()},cacheEntriesUpserted:{reducer(Q,s){for(let t of s.payload){let{queryDescription:r,value:i}=t;h(Q,r,!0,{arg:r,requestId:s.meta.requestId,startedTimeStamp:s.meta.timestamp}),R(Q,{arg:r,requestId:s.meta.requestId,fulfilledTimeStamp:s.meta.timestamp,baseQueryMeta:{}},i,!0)}},prepare:Q=>({payload:Q.map(r=>{let{endpointName:i,arg:l,value:E}=r,v=y[i];return{queryDescription:{type:"query",endpointName:i,originalArgs:r.arg,queryCacheKey:g({queryArgs:l,endpointDefinition:v,endpointName:i})},value:E}}),meta:{[ke]:!0,requestId:Be(),timestamp:Date.now()}})},queryResultPatched:{reducer(Q,{payload:{queryCacheKey:s,patches:t}}){ve(Q,s,r=>{r.data=lt(r.data,t.concat())})},prepare:ge()}},extraReducers(Q){Q.addCase(n.pending,(s,{meta:t,meta:{arg:r}})=>{let i=De(r);h(s,r,i,t)}).addCase(n.fulfilled,(s,{meta:t,payload:r})=>{let i=De(t.arg);R(s,t,r,i)}).addCase(n.rejected,(s,{meta:{condition:t,arg:r,requestId:i},error:l,payload:E})=>{ve(s,r.queryCacheKey,v=>{if(!t){if(v.requestId!==i)return;v.status="rejected",v.error=E??l}})}).addMatcher(k,(s,t)=>{let{queries:r}=T(t);for(let[i,l]of Object.entries(r))(l?.status==="fulfilled"||l?.status==="rejected")&&(s[i]=l)})}}),f=ae({name:`${e}/mutations`,initialState:Oe,reducers:{removeMutationResult:{reducer(Q,{payload:s}){let t=de(s);t in Q&&delete Q[t]},prepare:ge()}},extraReducers(Q){Q.addCase(d.pending,(s,{meta:t,meta:{requestId:r,arg:i,startedTimeStamp:l}})=>{i.track&&(s[de(t)]={requestId:r,status:"pending",endpointName:i.endpointName,startedTimeStamp:l})}).addCase(d.fulfilled,(s,{payload:t,meta:r})=>{r.arg.track&&ft(s,r,i=>{i.requestId===r.requestId&&(i.status="fulfilled",i.data=t,i.fulfilledTimeStamp=r.fulfilledTimeStamp)})}).addCase(d.rejected,(s,{payload:t,error:r,meta:i})=>{i.arg.track&&ft(s,i,l=>{l.requestId===i.requestId&&(l.status="rejected",l.error=t??r)})}).addMatcher(k,(s,t)=>{let{mutations:r}=T(t);for(let[i,l]of Object.entries(r))(l?.status==="fulfilled"||l?.status==="rejected")&&i!==l?.requestId&&(s[i]=l)})}}),B={tags:{},keys:{}},p=ae({name:`${e}/invalidation`,initialState:B,reducers:{updateProvidedBy:{reducer(Q,s){for(let{queryCacheKey:t,providedTags:r}of s.payload){o(Q,t);for(let{type:i,id:l}of r){let E=(Q.tags[i]??={})[l||"__internal_without_id"]??=[];E.includes(t)||E.push(t)}Q.keys[t]=r}},prepare:ge()}},extraReducers(Q){Q.addCase(a.actions.removeQueryResult,(s,{payload:{queryCacheKey:t}})=>{o(s,t)}).addMatcher(k,(s,t)=>{let{provided:r}=T(t);for(let[i,l]of Object.entries(r))for(let[E,v]of Object.entries(l)){let O=(s.tags[i]??={})[E||"__internal_without_id"]??=[];for(let C of v)O.includes(C)||O.push(C)}}).addMatcher(oe(W(n),me(n)),(s,t)=>{S(s,[t])}).addMatcher(a.actions.cacheEntriesUpserted.match,(s,t)=>{let r=t.payload.map(({queryDescription:i,value:l})=>({type:"UNKNOWN",payload:l,meta:{requestStatus:"fulfilled",requestId:"UNKNOWN",arg:i}}));S(s,r)})}});function o(Q,s){let t=Q.keys[s]??[];for(let r of t){let i=r.type,l=r.id??"__internal_without_id",E=Q.tags[i]?.[l];E&&(Q.tags[i][l]=E.filter(v=>v!==s))}delete Q.keys[s]}function S(Q,s){let t=s.map(r=>{let i=Fe(r,"providesTags",y,w),{queryCacheKey:l}=r.meta.arg;return{queryCacheKey:l,providedTags:i}});p.caseReducers.updateProvidedBy(Q,p.actions.updateProvidedBy(t))}let c=ae({name:`${e}/subscriptions`,initialState:Oe,reducers:{updateSubscriptionOptions(Q,s){},unsubscribeQueryResult(Q,s){},internal_getRTKQSubscriptions(){}}}),u=ae({name:`${e}/internalSubscriptions`,initialState:Oe,reducers:{subscriptionsUpdated:{reducer(Q,s){return lt(Q,s.payload)},prepare:ge()}}}),m=ae({name:`${e}/config`,initialState:{online:rt(),focused:nt(),middlewareRegistered:!1,...A},reducers:{middlewareRegistered(Q,{payload:s}){Q.middlewareRegistered=Q.middlewareRegistered==="conflict"||x!==s?"conflict":!0}},extraReducers:Q=>{Q.addCase(re,s=>{s.online=!0}).addCase(Te,s=>{s.online=!1}).addCase(ne,s=>{s.focused=!0}).addCase(Qe,s=>{s.focused=!1}).addMatcher(k,s=>({...s}))}}),D=Xe({queries:a.reducer,mutations:f.reducer,provided:p.reducer,subscriptions:u.reducer,config:m.reducer}),b=(Q,s)=>D(P.match(s)?void 0:Q,s),I={...m.actions,...a.actions,...c.actions,...u.actions,...f.actions,...p.actions,resetApiState:P};return{reducer:b,actions:I}}var Ne=Symbol.for("RTKQ/skipToken"),Tt={status:"uninitialized"},gt=Se(Tt,()=>{}),Qt=Se(Tt,()=>{});function ht({serializeQueryArgs:e,reducerPath:n,createSelector:d}){let g=c=>gt,y=c=>Qt;return{buildQuerySelector:R,buildInfiniteQuerySelector:a,buildMutationSelector:f,selectInvalidatedBy:B,selectCachedArgsForQuery:p,selectApiState:T,selectQueries:k,selectMutations:A,selectQueryEntry:w,selectConfig:P};function x(c){return{...c,...Le(c.status)}}function T(c){return c[n]}function k(c){return T(c)?.queries}function w(c,u){return k(c)?.[u]}function A(c){return T(c)?.mutations}function P(c){return T(c)?.config}function h(c,u,m){return D=>{if(D===Ne)return d(g,m);let b=e({queryArgs:D,endpointDefinition:u,endpointName:c});return d(Q=>w(Q,b)??gt,m)}}function R(c,u){return h(c,u,x)}function a(c,u){let{infiniteQueryOptions:m}=u;function D(b){let I={...b,...Le(b.status)},{isLoading:Q,isError:s,direction:t}=I,r=t==="forward",i=t==="backward";return{...I,hasNextPage:o(m,I.data,I.originalArgs),hasPreviousPage:S(m,I.data,I.originalArgs),isFetchingNextPage:Q&&r,isFetchingPreviousPage:Q&&i,isFetchNextPageError:s&&r,isFetchPreviousPageError:s&&i}}return h(c,u,D)}function f(){return c=>{let u;return typeof c=="object"?u=de(c)??Ne:u=c,d(u===Ne?y:b=>T(b)?.mutations?.[u]??Qt,x)}}function B(c,u){let m=c[n],D=new Set;for(let b of u.filter(se).map(we)){let I=m.provided.tags[b.type];if(!I)continue;let Q=(b.id!==void 0?I[b.id]:_e(Object.values(I)))??[];for(let s of Q)D.add(s)}return _e(Array.from(D.values()).map(b=>{let I=m.queries[b];return I?[{queryCacheKey:b,endpointName:I.endpointName,originalArgs:I.originalArgs}]:[]}))}function p(c,u){return Object.values(k(c)).filter(m=>m?.endpointName===u&&m.status!=="uninitialized").map(m=>m.originalArgs)}function o(c,u,m){return u?Ce(c,u,m)!=null:!1}function S(c,u,m){return!u||!c.getPreviousPageParam?!1:ze(c,u,m)!=null}}import{formatProdErrorMessage as Gt}from"@reduxjs/toolkit";var At=WeakMap?new WeakMap:void 0,qe=({endpointName:e,queryArgs:n})=>{let d="",g=At?.get(n);if(typeof g=="string")d=g;else{let y=JSON.stringify(n,(x,T)=>(T=typeof T=="bigint"?{$bigint:T.toString()}:T,T=te(T)?Object.keys(T).sort().reduce((k,w)=>(k[w]=T[w],k),{}):T,T));te(n)&&At?.set(n,y),d=y}return`${e}(${d})`};import{weakMapMemoize as Rt}from"reselect";function We(...e){return function(d){let g=Rt(A=>d.extractRehydrationInfo?.(A,{reducerPath:d.reducerPath??"api"})),y={reducerPath:"api",keepUnusedDataFor:60,refetchOnMountOrArgChange:!1,refetchOnFocus:!1,refetchOnReconnect:!1,invalidationBehavior:"delayed",...d,extractRehydrationInfo:g,serializeQueryArgs(A){let P=qe;if("serializeQueryArgs"in A.endpointDefinition){let h=A.endpointDefinition.serializeQueryArgs;P=R=>{let a=h(R);return typeof a=="string"?a:qe({...R,queryArgs:a})}}else d.serializeQueryArgs&&(P=d.serializeQueryArgs);return P(A)},tagTypes:[...d.tagTypes||[]]},x={endpointDefinitions:{},batch(A){A()},apiUid:Be(),extractRehydrationInfo:g,hasRehydrationInfo:Rt(A=>g(A)!=null)},T={injectEndpoints:w,enhanceEndpoints({addTagTypes:A,endpoints:P}){if(A)for(let h of A)y.tagTypes.includes(h)||y.tagTypes.push(h);if(P)for(let[h,R]of Object.entries(P))typeof R=="function"?R(x.endpointDefinitions[h]):Object.assign(x.endpointDefinitions[h]||{},R);return T}},k=e.map(A=>A.init(T,y,x));function w(A){let P=A.endpoints({query:h=>({...h,type:"query"}),mutation:h=>({...h,type:"mutation"}),infiniteQuery:h=>({...h,type:"infinitequery"})});for(let[h,R]of Object.entries(P)){if(A.overrideExisting!==!0&&h in x.endpointDefinitions){if(A.overrideExisting==="throw")throw new Error(Gt(39));continue}x.endpointDefinitions[h]=R;for(let a of k)a.injectEndpoint(h,R)}return T}return T.injectEndpoints({endpoints:d.endpoints})}}import{formatProdErrorMessage as Yt}from"@reduxjs/toolkit";var Xt=Symbol();function Zt(){return function(){throw new Error(Yt(33))}}import{enablePatches as rn}from"immer";function Y(e,...n){return Object.assign(e,...n)}import{produceWithPatches as en}from"immer";var St=({api:e,queryThunk:n,internalState:d})=>{let g=`${e.reducerPath}/subscriptions`,y=null,x=null,{updateSubscriptionOptions:T,unsubscribeQueryResult:k}=e.internalActions,w=(a,f)=>{if(T.match(f)){let{queryCacheKey:p,requestId:o,options:S}=f.payload;return a?.[p]?.[o]&&(a[p][o]=S),!0}if(k.match(f)){let{queryCacheKey:p,requestId:o}=f.payload;return a[p]&&delete a[p][o],!0}if(e.internalActions.removeQueryResult.match(f))return delete a[f.payload.queryCacheKey],!0;if(n.pending.match(f)){let{meta:{arg:p,requestId:o}}=f,S=a[p.queryCacheKey]??={};return S[`${o}_running`]={},p.subscribe&&(S[o]=p.subscriptionOptions??S[o]??{}),!0}let B=!1;if(n.fulfilled.match(f)||n.rejected.match(f)){let p=a[f.meta.arg.queryCacheKey]||{},o=`${f.meta.requestId}_running`;B||=!!p[o],delete p[o]}if(n.rejected.match(f)){let{meta:{condition:p,arg:o,requestId:S}}=f;if(p&&o.subscribe){let c=a[o.queryCacheKey]??={};c[S]=o.subscriptionOptions??c[S]??{},B=!0}}return B},A=()=>d.currentSubscriptions,R={getSubscriptions:A,getSubscriptionCount:a=>{let B=A()[a]??{};return J(B)},isRequestSubscribed:(a,f)=>!!A()?.[a]?.[f]};return(a,f)=>{if(y||(y=JSON.parse(JSON.stringify(d.currentSubscriptions))),e.util.resetApiState.match(a))return y=d.currentSubscriptions={},x=null,[!0,!1];if(e.internalActions.internal_getRTKQSubscriptions.match(a))return[!1,R];let B=w(d.currentSubscriptions,a),p=!0;if(B){x||(x=setTimeout(()=>{let c=JSON.parse(JSON.stringify(d.currentSubscriptions)),[,u]=en(y,()=>c);f.next(e.internalActions.subscriptionsUpdated(u)),y=c,x=null},500));let o=typeof a.type=="string"&&!!a.type.startsWith(g),S=n.rejected.match(a)&&a.meta.condition&&!!a.meta.arg.subscribe;p=!o&&!S}return[p,!1]}};function tn(e){for(let n in e)return!1;return!0}var nn=2147483647/1e3-1,xt=({reducerPath:e,api:n,queryThunk:d,context:g,internalState:y,selectors:{selectQueryEntry:x,selectConfig:T}})=>{let{removeQueryResult:k,unsubscribeQueryResult:w,cacheEntriesUpserted:A}=n.internalActions,P=oe(w.match,d.fulfilled,d.rejected,A.match);function h(p){let o=y.currentSubscriptions[p];return!!o&&!tn(o)}let R={},a=(p,o,S)=>{let c=o.getState(),u=T(c);if(P(p)){let m;if(A.match(p))m=p.payload.map(D=>D.queryDescription.queryCacheKey);else{let{queryCacheKey:D}=w.match(p)?p.payload:p.meta.arg;m=[D]}f(m,o,u)}if(n.util.resetApiState.match(p))for(let[m,D]of Object.entries(R))D&&clearTimeout(D),delete R[m];if(g.hasRehydrationInfo(p)){let{queries:m}=g.extractRehydrationInfo(p);f(Object.keys(m),o,u)}};function f(p,o,S){let c=o.getState();for(let u of p){let m=x(c,u);B(u,m?.endpointName,o,S)}}function B(p,o,S,c){let m=g.endpointDefinitions[o]?.keepUnusedDataFor??c.keepUnusedDataFor;if(m===1/0)return;let D=Math.max(0,Math.min(m,nn));if(!h(p)){let b=R[p];b&&clearTimeout(b),R[p]=setTimeout(()=>{h(p)||S.dispatch(k({queryCacheKey:p})),delete R[p]},D*1e3)}}return a};var Dt=new Error("Promise never resolved before cacheEntryRemoved."),bt=({api:e,reducerPath:n,context:d,queryThunk:g,mutationThunk:y,internalState:x,selectors:{selectQueryEntry:T,selectApiState:k}})=>{let w=He(g),A=He(y),P=W(g,y),h={};function R(o,S,c){let u=h[o];u?.valueResolved&&(u.valueResolved({data:S,meta:c}),delete u.valueResolved)}function a(o){let S=h[o];S&&(delete h[o],S.cacheEntryRemoved())}let f=(o,S,c)=>{let u=B(o);function m(D,b,I,Q){let s=T(c,b),t=T(S.getState(),b);!s&&t&&p(D,Q,b,S,I)}if(g.pending.match(o))m(o.meta.arg.endpointName,u,o.meta.requestId,o.meta.arg.originalArgs);else if(e.internalActions.cacheEntriesUpserted.match(o))for(let{queryDescription:D,value:b}of o.payload){let{endpointName:I,originalArgs:Q,queryCacheKey:s}=D;m(I,s,o.meta.requestId,Q),R(s,b,{})}else if(y.pending.match(o))S.getState()[n].mutations[u]&&p(o.meta.arg.endpointName,o.meta.arg.originalArgs,u,S,o.meta.requestId);else if(P(o))R(u,o.payload,o.meta.baseQueryMeta);else if(e.internalActions.removeQueryResult.match(o)||e.internalActions.removeMutationResult.match(o))a(u);else if(e.util.resetApiState.match(o))for(let D of Object.keys(h))a(D)};function B(o){return w(o)?o.meta.arg.queryCacheKey:A(o)?o.meta.arg.fixedCacheKey??o.meta.requestId:e.internalActions.removeQueryResult.match(o)?o.payload.queryCacheKey:e.internalActions.removeMutationResult.match(o)?de(o.payload):""}function p(o,S,c,u,m){let D=d.endpointDefinitions[o],b=D?.onCacheEntryAdded;if(!b)return;let I={},Q=new Promise(E=>{I.cacheEntryRemoved=E}),s=Promise.race([new Promise(E=>{I.valueResolved=E}),Q.then(()=>{throw Dt})]);s.catch(()=>{}),h[c]=I;let t=e.endpoints[o].select(he(D)?S:c),r=u.dispatch((E,v,O)=>O),i={...u,getCacheEntry:()=>t(u.getState()),requestId:m,extra:r,updateCachedData:he(D)?E=>u.dispatch(e.util.updateQueryData(o,S,E)):void 0,cacheDataLoaded:s,cacheEntryRemoved:Q},l=b(S,i);Promise.resolve(l).catch(E=>{if(E!==Dt)throw E})}return f};var Et=({api:e,context:{apiUid:n},reducerPath:d})=>(g,y)=>{e.util.resetApiState.match(g)&&y.dispatch(e.internalActions.middlewareRegistered(n))};var Pt=({reducerPath:e,context:n,context:{endpointDefinitions:d},mutationThunk:g,queryThunk:y,api:x,assertTagType:T,refetchQuery:k,internalState:w})=>{let{removeQueryResult:A}=x.internalActions,P=oe(W(g),me(g)),h=oe(W(g,y),fe(g,y)),R=[],a=(p,o)=>{P(p)?B(Fe(p,"invalidatesTags",d,T),o):h(p)?B([],o):x.util.invalidateTags.match(p)&&B(xe(p.payload,void 0,void 0,void 0,void 0,T),o)};function f(p){let{queries:o,mutations:S}=p;for(let c of[o,S])for(let u in c)if(c[u]?.status==="pending")return!0;return!1}function B(p,o){let S=o.getState(),c=S[e];if(R.push(...p),c.config.invalidationBehavior==="delayed"&&f(c))return;let u=R;if(R=[],u.length===0)return;let m=x.util.selectInvalidatedBy(S,u);n.batch(()=>{let D=Array.from(m.values());for(let{queryCacheKey:b}of D){let I=c.queries[b],Q=w.currentSubscriptions[b]??{};I&&(J(Q)===0?o.dispatch(A({queryCacheKey:b})):I.status!=="uninitialized"&&o.dispatch(k(I)))}})}return a};var It=({reducerPath:e,queryThunk:n,api:d,refetchQuery:g,internalState:y})=>{let x={},T=(a,f)=>{(d.internalActions.updateSubscriptionOptions.match(a)||d.internalActions.unsubscribeQueryResult.match(a))&&A(a.payload,f),(n.pending.match(a)||n.rejected.match(a)&&a.meta.condition)&&A(a.meta.arg,f),(n.fulfilled.match(a)||n.rejected.match(a)&&!a.meta.condition)&&w(a.meta.arg,f),d.util.resetApiState.match(a)&&h()};function k(a,f){let p=f.getState()[e].queries[a],o=y.currentSubscriptions[a];if(!(!p||p.status==="uninitialized"))return o}function w({queryCacheKey:a},f){let B=f.getState()[e],p=B.queries[a],o=y.currentSubscriptions[a];if(!p||p.status==="uninitialized")return;let{lowestPollingInterval:S,skipPollingIfUnfocused:c}=R(o);if(!Number.isFinite(S))return;let u=x[a];u?.timeout&&(clearTimeout(u.timeout),u.timeout=void 0);let m=Date.now()+S;x[a]={nextPollTimestamp:m,pollingInterval:S,timeout:setTimeout(()=>{(B.config.focused||!c)&&f.dispatch(g(p)),w({queryCacheKey:a},f)},S)}}function A({queryCacheKey:a},f){let p=f.getState()[e].queries[a],o=y.currentSubscriptions[a];if(!p||p.status==="uninitialized")return;let{lowestPollingInterval:S}=R(o);if(!Number.isFinite(S)){P(a);return}let c=x[a],u=Date.now()+S;(!c||u<c.nextPollTimestamp)&&w({queryCacheKey:a},f)}function P(a){let f=x[a];f?.timeout&&clearTimeout(f.timeout),delete x[a]}function h(){for(let a of Object.keys(x))P(a)}function R(a={}){let f=!1,B=Number.POSITIVE_INFINITY;for(let p in a)a[p].pollingInterval&&(B=Math.min(a[p].pollingInterval,B),f=a[p].skipPollingIfUnfocused||f);return{lowestPollingInterval:B,skipPollingIfUnfocused:f}}return T};var kt=({api:e,context:n,queryThunk:d,mutationThunk:g})=>{let y=Ie(d,g),x=fe(d,g),T=W(d,g),k={};return(A,P)=>{if(y(A)){let{requestId:h,arg:{endpointName:R,originalArgs:a}}=A.meta,f=n.endpointDefinitions[R],B=f?.onQueryStarted;if(B){let p={},o=new Promise((m,D)=>{p.resolve=m,p.reject=D});o.catch(()=>{}),k[h]=p;let S=e.endpoints[R].select(he(f)?a:h),c=P.dispatch((m,D,b)=>b),u={...P,getCacheEntry:()=>S(P.getState()),requestId:h,extra:c,updateCachedData:he(f)?m=>P.dispatch(e.util.updateQueryData(R,a,m)):void 0,queryFulfilled:o};B(a,u)}}else if(T(A)){let{requestId:h,baseQueryMeta:R}=A.meta;k[h]?.resolve({data:A.payload,meta:R}),delete k[h]}else if(x(A)){let{requestId:h,rejectedWithValue:R,baseQueryMeta:a}=A.meta;k[h]?.reject({error:A.payload??A.error,isUnhandledError:!R,meta:a}),delete k[h]}}};var Bt=({reducerPath:e,context:n,api:d,refetchQuery:g,internalState:y})=>{let{removeQueryResult:x}=d.internalActions,T=(w,A)=>{ne.match(w)&&k(A,"refetchOnFocus"),re.match(w)&&k(A,"refetchOnReconnect")};function k(w,A){let P=w.getState()[e],h=P.queries,R=y.currentSubscriptions;n.batch(()=>{for(let a of Object.keys(R)){let f=h[a],B=R[a];if(!B||!f)continue;(Object.values(B).some(o=>o[A]===!0)||Object.values(B).every(o=>o[A]===void 0)&&P.config[A])&&(J(B)===0?w.dispatch(x({queryCacheKey:a})):f.status!=="uninitialized"&&w.dispatch(g(f)))}})}return T};function Mt(e){let{reducerPath:n,queryThunk:d,api:g,context:y}=e,{apiUid:x}=y,T={invalidateTags:X(`${n}/invalidateTags`)},k=h=>h.type.startsWith(`${n}/`),w=[Et,xt,Pt,It,bt,kt];return{middleware:h=>{let R=!1,f={...e,internalState:{currentSubscriptions:{}},refetchQuery:P,isThisApiSliceAction:k},B=w.map(S=>S(f)),p=St(f),o=Bt(f);return S=>c=>{if(!Ze(c))return S(c);R||(R=!0,h.dispatch(g.internalActions.middlewareRegistered(x)));let u={...h,next:S},m=h.getState(),[D,b]=p(c,u,m),I;if(D?I=S(c):I=b,h.getState()[n]&&(o(c,u,m),k(c)||y.hasRehydrationInfo(c)))for(let Q of B)Q(c,u,m);return I}},actions:T};function P(h){return e.api.endpoints[h.endpointName].initiate(h.originalArgs,{subscribe:!1,forceRefetch:!0})}}var Ke=Symbol(),$e=({createSelector:e=Ye}={})=>({name:Ke,init(n,{baseQuery:d,tagTypes:g,reducerPath:y,serializeQueryArgs:x,keepUnusedDataFor:T,refetchOnMountOrArgChange:k,refetchOnFocus:w,refetchOnReconnect:A,invalidationBehavior:P,onSchemaFailure:h,catchSchemaFailure:R,skipSchemaValidation:a},f){rn();let B=F=>F;Object.assign(n,{reducerPath:y,endpoints:{},internalActions:{onOnline:re,onOffline:Te,onFocus:ne,onFocusLost:Qe},util:{}});let p=ht({serializeQueryArgs:x,reducerPath:y,createSelector:e}),{selectInvalidatedBy:o,selectCachedArgsForQuery:S,buildQuerySelector:c,buildInfiniteQuerySelector:u,buildMutationSelector:m}=p;Y(n.util,{selectInvalidatedBy:o,selectCachedArgsForQuery:S});let{queryThunk:D,infiniteQueryThunk:b,mutationThunk:I,patchQueryData:Q,updateQueryData:s,upsertQueryData:t,prefetch:r,buildMatchThunkActions:i}=ct({baseQuery:d,reducerPath:y,context:f,api:n,serializeQueryArgs:x,assertTagType:B,selectors:p,onSchemaFailure:h,catchSchemaFailure:R,skipSchemaValidation:a}),{reducer:l,actions:E}=mt({context:f,queryThunk:D,infiniteQueryThunk:b,mutationThunk:I,serializeQueryArgs:x,reducerPath:y,assertTagType:B,config:{refetchOnFocus:w,refetchOnReconnect:A,refetchOnMountOrArgChange:k,keepUnusedDataFor:T,reducerPath:y,invalidationBehavior:P}});Y(n.util,{patchQueryData:Q,updateQueryData:s,upsertQueryData:t,prefetch:r,resetApiState:E.resetApiState,upsertQueryEntries:E.cacheEntriesUpserted}),Y(n.internalActions,E);let{middleware:v,actions:O}=Mt({reducerPath:y,context:f,queryThunk:D,mutationThunk:I,infiniteQueryThunk:b,api:n,assertTagType:B,selectors:p});Y(n.util,O),Y(n,{reducer:l,middleware:v});let{buildInitiateQuery:C,buildInitiateInfiniteQuery:M,buildInitiateMutation:K,getRunningMutationThunk:j,getRunningMutationsThunk:z,getRunningQueriesThunk:L,getRunningQueryThunk:N}=pt({queryThunk:D,mutationThunk:I,infiniteQueryThunk:b,api:n,serializeQueryArgs:x,context:f});return Y(n.util,{getRunningMutationThunk:j,getRunningMutationsThunk:z,getRunningQueryThunk:N,getRunningQueriesThunk:L}),{name:Ke,injectEndpoint(F,_){let H=n,q=H.endpoints[F]??={};ue(_)&&Y(q,{name:F,select:c(F,_),initiate:C(F,_)},i(D,F)),yt(_)&&Y(q,{name:F,select:m(),initiate:K(F)},i(I,F)),ye(_)&&Y(q,{name:F,select:u(F,_),initiate:M(F,_)},i(D,F))}}}});var an=We($e());export{Re as NamedSchemaError,Ue as QueryStatus,Xt as _NEVER,We as buildCreateApi,Me as copyWithStructuralSharing,$e as coreModule,Ke as coreModuleName,an as createApi,qe as defaultSerializeQueryArgs,Zt as fakeBaseQuery,Nt as fetchBaseQuery,Lt as retry,jt as setupListeners,Ne as skipToken};
//# sourceMappingURL=rtk-query.browser.mjs.map