"use strict";var Pe=Object.defineProperty;var jt=Object.getOwnPropertyDescriptor;var Ft=Object.getOwnPropertyNames;var Vt=Object.prototype.hasOwnProperty;var _t=(e,t)=>{for(var r in t)Pe(e,r,{get:t[r],enumerable:!0})},Me=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let o of Ft(t))!Vt.call(e,o)&&o!==r&&Pe(e,o,{get:()=>t[o],enumerable:!(n=jt(t,o))||n.enumerable});return e},w=(e,t,r)=>(Me(e,t,"default"),r&&Me(r,t,"default"));var Lt=e=>Me(Pe({},"__esModule",{value:!0}),e);var C={};_t(C,{ReducerType:()=>We,SHOULD_AUTOBATCH:()=>ce,TaskAbortError:()=>I,Tuple:()=>F,addListener:()=>Re,asyncThunkCreator:()=>lt,autoBatchEnhancer:()=>de,buildCreateSlice:()=>ze,clearAllListeners:()=>qe,combineSlices:()=>Nt,configureStore:()=>st,createAction:()=>b,createActionCreatorInvariantMiddleware:()=>Qe,createAsyncThunk:()=>ye,createDraftSafeSelector:()=>ne,createDraftSafeSelectorCreator:()=>be,createDynamicMiddleware:()=>Dt,createEntityAdapter:()=>Tt,createImmutableStateInvariantMiddleware:()=>Ze,createListenerMiddleware:()=>It,createNextState:()=>N.produce,createReducer:()=>le,createSelector:()=>W.createSelector,createSelectorCreator:()=>W.createSelectorCreator,createSerializableStateInvariantMiddleware:()=>nt,createSlice:()=>pt,current:()=>N.current,findNonSerializableValue:()=>Ne,formatProdErrorMessage:()=>x,freeze:()=>N.freeze,isActionCreator:()=>oe,isAllOf:()=>B,isAnyOf:()=>V,isAsyncThunkAction:()=>_e,isDraft:()=>N.isDraft,isFluxStandardAction:()=>ae,isFulfilled:()=>Ve,isImmutableDefault:()=>Ye,isPending:()=>je,isPlain:()=>Oe,isRejected:()=>$,isRejectedWithValue:()=>Fe,lruMemoize:()=>W.lruMemoize,miniSerializeError:()=>Le,nanoid:()=>O,original:()=>N.original,prepareAutoBatched:()=>at,removeListener:()=>we,unwrapResult:()=>Ue,weakMapMemoize:()=>W.weakMapMemoize});module.exports=Lt(C);w(C,require("redux"),module.exports);var N=require("immer"),W=require("reselect");var ee=require("immer"),te=require("reselect"),be=(...e)=>{let t=(0,te.createSelectorCreator)(...e),r=Object.assign((...n)=>{let o=t(...n),a=(s,...y)=>o((0,ee.isDraft)(s)?(0,ee.current)(s):s,...y);return Object.assign(a,o),a},{withTypes:()=>r});return r},ne=be(te.weakMapMemoize);var D=require("redux");var Ie=require("redux"),Je=typeof window<"u"&&window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__?window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__:function(){if(arguments.length!==0)return typeof arguments[0]=="object"?Ie.compose:Ie.compose.apply(null,arguments)},xn=typeof window<"u"&&window.__REDUX_DEVTOOLS_EXTENSION__?window.__REDUX_DEVTOOLS_EXTENSION__:function(){return function(e){return e}};var se=require("redux-thunk");var ve=require("redux");var re=e=>e&&typeof e.match=="function";function b(e,t){function r(...n){if(t){let o=t(...n);if(!o)throw new Error(x(0));return{type:e,payload:o.payload,..."meta"in o&&{meta:o.meta},..."error"in o&&{error:o.error}}}return{type:e,payload:n[0]}}return r.toString=()=>`${e}`,r.type=e,r.match=n=>(0,ve.isAction)(n)&&n.type===e,r}function oe(e){return typeof e=="function"&&"type"in e&&re(e)}function ae(e){return(0,ve.isAction)(e)&&Object.keys(e).every(Ut)}function Ut(e){return["type","payload","error","meta"].indexOf(e)>-1}function Wt(e){let t=e?`${e}`.split("/"):[],r=t[t.length-1]||"actionCreator";return`Detected an action creator with type "${e||"unknown"}" being dispatched. 
Make sure you're calling the action creator before dispatching, i.e. \`dispatch(${r}())\` instead of \`dispatch(${r})\`. This is necessary even if the action has no payload.`}function Qe(e={}){return()=>r=>n=>r(n)}var ie=require("immer");var F=class e extends Array{constructor(...t){super(...t),Object.setPrototypeOf(this,e.prototype)}static get[Symbol.species](){return e}concat(...t){return super.concat.apply(this,t)}prepend(...t){return t.length===1&&Array.isArray(t[0])?new e(...t[0].concat(this)):new e(...t.concat(this))}};function De(e){return(0,ie.isDraftable)(e)?(0,ie.produce)(e,()=>{}):e}function v(e,t,r){return e.has(t)?e.get(t):e.set(t,r(t)).get(t)}function Ye(e){return typeof e!="object"||e==null||Object.isFrozen(e)}function Ze(e={}){if(1)return()=>n=>o=>n(o);var t,r}var et=require("redux");function Oe(e){let t=typeof e;return e==null||t==="string"||t==="boolean"||t==="number"||Array.isArray(e)||(0,et.isPlainObject)(e)}function Ne(e,t="",r=Oe,n,o=[],a){let s;if(!r(e))return{keyPath:t||"<root>",value:e};if(typeof e!="object"||e===null||a?.has(e))return!1;let y=n!=null?n(e):Object.entries(e),c=o.length>0;for(let[l,i]of y){let d=t?t+"."+l:l;if(!(c&&o.some(g=>g instanceof RegExp?g.test(d):d===g))){if(!r(i))return{keyPath:d,value:i};if(typeof i=="object"&&(s=Ne(i,d,r,n,o,a),s))return s}}return a&&tt(e)&&a.add(e),!1}function tt(e){if(!Object.isFrozen(e))return!1;for(let t of Object.values(e))if(!(typeof t!="object"||t===null)&&!tt(t))return!1;return!0}function nt(e={}){return()=>t=>r=>t(r)}function zt(e){return typeof e=="boolean"}var rt=()=>function(t){let{thunk:r=!0,immutableCheck:n=!0,serializableCheck:o=!0,actionCreatorCheck:a=!0}=t??{},s=new F;return r&&(zt(r)?s.push(se.thunk):s.push((0,se.withExtraArgument)(r.extraArgument))),s};var ce="RTK_autoBatch",at=()=>e=>({payload:e,meta:{[ce]:!0}}),ot=e=>t=>{setTimeout(t,e)},de=(e={type:"raf"})=>t=>(...r)=>{let n=t(...r),o=!0,a=!1,s=!1,y=new Set,c=e.type==="tick"?queueMicrotask:e.type==="raf"?typeof window<"u"&&window.requestAnimationFrame?window.requestAnimationFrame:ot(10):e.type==="callback"?e.queueNotification:ot(e.timeout),l=()=>{s=!1,a&&(a=!1,y.forEach(i=>i()))};return Object.assign({},n,{subscribe(i){let d=()=>o&&i(),T=n.subscribe(d);return y.add(i),()=>{T(),y.delete(i)}},dispatch(i){try{return o=!i?.meta?.[ce],a=!o,a&&(s||(s=!0,c(l))),n.dispatch(i)}finally{o=!0}}})};var it=e=>function(r){let{autoBatch:n=!0}=r??{},o=new F(e);return n&&o.push(de(typeof n=="object"?n:void 0)),o};function st(e){let t=rt(),{reducer:r=void 0,middleware:n,devTools:o=!0,duplicateMiddlewareCheck:a=!0,preloadedState:s=void 0,enhancers:y=void 0}=e||{},c;if(typeof r=="function")c=r;else if((0,D.isPlainObject)(r))c=(0,D.combineReducers)(r);else throw new Error(x(1));let l;typeof n=="function"?l=n(t):l=t();let i=D.compose;o&&(i=Je({trace:!1,...typeof o=="object"&&o}));let d=(0,D.applyMiddleware)(...l),T=it(d),g=typeof y=="function"?y(T):T(),p=i(...g);return(0,D.createStore)(c,s,p)}var G=require("immer");function ue(e){let t={},r=[],n,o={addCase(a,s){let y=typeof a=="string"?a:a.type;if(!y)throw new Error(x(28));if(y in t)throw new Error(x(29));return t[y]=s,o},addMatcher(a,s){return r.push({matcher:a,reducer:s}),o},addDefaultCase(a){return n=a,o}};return e(o),[t,r,n]}function Gt(e){return typeof e=="function"}function le(e,t){let[r,n,o]=ue(t),a;if(Gt(e))a=()=>De(e());else{let y=De(e);a=()=>y}function s(y=a(),c){let l=[r[c.type],...n.filter(({matcher:i})=>i(c)).map(({reducer:i})=>i)];return l.filter(i=>!!i).length===0&&(l=[o]),l.reduce((i,d)=>{if(d)if((0,G.isDraft)(i)){let g=d(i,c);return g===void 0?i:g}else{if((0,G.isDraftable)(i))return(0,G.produce)(i,T=>d(T,c));{let T=d(i,c);if(T===void 0){if(i===null)return i;throw Error("A case reducer on a non-draftable value must not return undefined")}return T}}return i},y)}return s.getInitialState=a,s}var ct=(e,t)=>re(e)?e.match(t):e(t);function V(...e){return t=>e.some(r=>ct(r,t))}function B(...e){return t=>e.every(r=>ct(r,t))}function pe(e,t){if(!e||!e.meta)return!1;let r=typeof e.meta.requestId=="string",n=t.indexOf(e.meta.requestStatus)>-1;return r&&n}function X(e){return typeof e[0]=="function"&&"pending"in e[0]&&"fulfilled"in e[0]&&"rejected"in e[0]}function je(...e){return e.length===0?t=>pe(t,["pending"]):X(e)?V(...e.map(t=>t.pending)):je()(e[0])}function $(...e){return e.length===0?t=>pe(t,["rejected"]):X(e)?V(...e.map(t=>t.rejected)):$()(e[0])}function Fe(...e){let t=r=>r&&r.meta&&r.meta.rejectedWithValue;return e.length===0?B($(...e),t):X(e)?B($(...e),t):Fe()(e[0])}function Ve(...e){return e.length===0?t=>pe(t,["fulfilled"]):X(e)?V(...e.map(t=>t.fulfilled)):Ve()(e[0])}function _e(...e){return e.length===0?t=>pe(t,["pending","fulfilled","rejected"]):X(e)?V(...e.flatMap(t=>[t.pending,t.rejected,t.fulfilled])):_e()(e[0])}var Bt="ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW",O=(e=21)=>{let t="",r=e;for(;r--;)t+=Bt[Math.random()*64|0];return t};var Kt=["name","message","stack","code"],J=class{constructor(t,r){this.payload=t;this.meta=r}_type},fe=class{constructor(t,r){this.payload=t;this.meta=r}_type},Le=e=>{if(typeof e=="object"&&e!==null){let t={};for(let r of Kt)typeof e[r]=="string"&&(t[r]=e[r]);return t}return{message:String(e)}},dt="External signal was aborted",ye=(()=>{function e(t,r,n){let o=b(t+"/fulfilled",(c,l,i,d)=>({payload:c,meta:{...d||{},arg:i,requestId:l,requestStatus:"fulfilled"}})),a=b(t+"/pending",(c,l,i)=>({payload:void 0,meta:{...i||{},arg:l,requestId:c,requestStatus:"pending"}})),s=b(t+"/rejected",(c,l,i,d,T)=>({payload:d,error:(n&&n.serializeError||Le)(c||"Rejected"),meta:{...T||{},arg:i,requestId:l,rejectedWithValue:!!d,requestStatus:"rejected",aborted:c?.name==="AbortError",condition:c?.name==="ConditionError"}}));function y(c,{signal:l}={}){return(i,d,T)=>{let g=n?.idGenerator?n.idGenerator(c):O(),p=new AbortController,h,u;function f(A){u=A,p.abort()}l&&(l.aborted?f(dt):l.addEventListener("abort",()=>f(dt),{once:!0}));let k=async function(){let A;try{let S=n?.condition?.(c,{getState:d,extra:T});if(Ht(S)&&(S=await S),S===!1||p.signal.aborted)throw{name:"ConditionError",message:"Aborted due to condition callback returning false."};let P=new Promise((E,R)=>{h=()=>{R({name:"AbortError",message:u||"Aborted"})},p.signal.addEventListener("abort",h)});i(a(g,c,n?.getPendingMeta?.({requestId:g,arg:c},{getState:d,extra:T}))),A=await Promise.race([P,Promise.resolve(r(c,{dispatch:i,getState:d,extra:T,requestId:g,signal:p.signal,abort:f,rejectWithValue:(E,R)=>new J(E,R),fulfillWithValue:(E,R)=>new fe(E,R)})).then(E=>{if(E instanceof J)throw E;return E instanceof fe?o(E.payload,g,c,E.meta):o(E,g,c)})])}catch(S){A=S instanceof J?s(null,g,c,S.payload,S.meta):s(S,g,c)}finally{h&&p.signal.removeEventListener("abort",h)}return n&&!n.dispatchConditionRejection&&s.match(A)&&A.meta.condition||i(A),A}();return Object.assign(k,{abort:f,requestId:g,arg:c,unwrap(){return k.then(Ue)}})}}return Object.assign(y,{pending:a,rejected:s,fulfilled:o,settled:V(s,o),typePrefix:t})}return e.withTypes=()=>e,e})();function Ue(e){if(e.meta&&e.meta.rejectedWithValue)throw e.payload;if(e.error)throw e.error;return e.payload}function Ht(e){return e!==null&&typeof e=="object"&&typeof e.then=="function"}var ut=Symbol.for("rtk-slice-createasyncthunk"),lt={[ut]:ye},We=(n=>(n.reducer="reducer",n.reducerWithPrepare="reducerWithPrepare",n.asyncThunk="asyncThunk",n))(We||{});function qt(e,t){return`${e}/${t}`}function ze({creators:e}={}){let t=e?.asyncThunk?.[ut];return function(n){let{name:o,reducerPath:a=o}=n;if(!o)throw new Error(x(11));typeof process<"u";let s=(typeof n.reducers=="function"?n.reducers(Xt()):n.reducers)||{},y=Object.keys(s),c={sliceCaseReducersByName:{},sliceCaseReducersByType:{},actionCreators:{},sliceMatchers:[]},l={addCase(A,m){let S=typeof A=="string"?A:A.type;if(!S)throw new Error(x(12));if(S in c.sliceCaseReducersByType)throw new Error(x(13));return c.sliceCaseReducersByType[S]=m,l},addMatcher(A,m){return c.sliceMatchers.push({matcher:A,reducer:m}),l},exposeAction(A,m){return c.actionCreators[A]=m,l},exposeCaseReducer(A,m){return c.sliceCaseReducersByName[A]=m,l}};y.forEach(A=>{let m=s[A],S={reducerName:A,type:qt(o,A),createNotation:typeof n.reducers=="function"};Qt(m)?Zt(S,m,l,t):Jt(S,m,l)});function i(){let[A={},m=[],S=void 0]=typeof n.extraReducers=="function"?ue(n.extraReducers):[n.extraReducers],P={...A,...c.sliceCaseReducersByType};return le(n.initialState,E=>{for(let R in P)E.addCase(R,P[R]);for(let R of c.sliceMatchers)E.addMatcher(R.matcher,R.reducer);for(let R of m)E.addMatcher(R.matcher,R.reducer);S&&E.addDefaultCase(S)})}let d=A=>A,T=new Map,g=new WeakMap,p;function h(A,m){return p||(p=i()),p(A,m)}function u(){return p||(p=i()),p.getInitialState()}function f(A,m=!1){function S(E){let R=E[A];return typeof R>"u"&&m&&(R=v(g,S,u)),R}function P(E=d){let R=v(T,m,()=>new WeakMap);return v(R,E,()=>{let q={};for(let[Z,z]of Object.entries(n.selectors??{}))q[Z]=$t(z,E,()=>v(g,E,u),m);return q})}return{reducerPath:A,getSelectors:P,get selectors(){return P(S)},selectSlice:S}}let k={name:o,reducer:h,actions:c.actionCreators,caseReducers:c.sliceCaseReducersByName,getInitialState:u,...f(a),injectInto(A,{reducerPath:m,...S}={}){let P=m??a;return A.inject({reducerPath:P,reducer:h},S),{...k,...f(P,!0)}}};return k}}function $t(e,t,r,n){function o(a,...s){let y=t(a);return typeof y>"u"&&n&&(y=r()),e(y,...s)}return o.unwrapped=e,o}var pt=ze();function Xt(){function e(t,r){return{_reducerDefinitionType:"asyncThunk",payloadCreator:t,...r}}return e.withTypes=()=>e,{reducer(t){return Object.assign({[t.name](...r){return t(...r)}}[t.name],{_reducerDefinitionType:"reducer"})},preparedReducer(t,r){return{_reducerDefinitionType:"reducerWithPrepare",prepare:t,reducer:r}},asyncThunk:e}}function Jt({type:e,reducerName:t,createNotation:r},n,o){let a,s;if("reducer"in n){if(r&&!Yt(n))throw new Error(x(17));a=n.reducer,s=n.prepare}else a=n;o.addCase(e,a).exposeCaseReducer(t,a).exposeAction(t,s?b(e,s):b(e))}function Qt(e){return e._reducerDefinitionType==="asyncThunk"}function Yt(e){return e._reducerDefinitionType==="reducerWithPrepare"}function Zt({type:e,reducerName:t},r,n,o){if(!o)throw new Error(x(18));let{payloadCreator:a,fulfilled:s,pending:y,rejected:c,settled:l,options:i}=r,d=o(e,a,i);n.exposeAction(t,d),s&&n.addCase(d.fulfilled,s),y&&n.addCase(d.pending,y),c&&n.addCase(d.rejected,c),l&&n.addMatcher(d.settled,l),n.exposeCaseReducer(t,{fulfilled:s||he,pending:y||he,rejected:c||he,settled:l||he})}function he(){}function en(){return{ids:[],entities:{}}}function ft(e){function t(r={},n){let o=Object.assign(en(),r);return n?e.setAll(o,n):o}return{getInitialState:t}}function yt(){function e(t,r={}){let{createSelector:n=ne}=r,o=d=>d.ids,a=d=>d.entities,s=n(o,a,(d,T)=>d.map(g=>T[g])),y=(d,T)=>T,c=(d,T)=>d[T],l=n(o,d=>d.length);if(!t)return{selectIds:o,selectEntities:a,selectAll:s,selectTotal:l,selectById:n(a,y,c)};let i=n(t,a);return{selectIds:n(t,o),selectEntities:i,selectAll:n(t,s),selectTotal:n(t,l),selectById:n(i,y,c)}}return{getSelectors:e}}var Ae=require("immer");var tn=Ae.isDraft;function ht(e){let t=M((r,n)=>e(n));return function(n){return t(n,void 0)}}function M(e){return function(r,n){function o(s){return ae(s)}let a=s=>{o(n)?e(n.payload,s):e(n,s)};return tn(r)?(a(r),r):(0,Ae.produce)(r,a)}}var Te=require("immer");function _(e,t){return t(e)}function j(e){return Array.isArray(e)||(e=Object.values(e)),e}function Q(e){return(0,Te.isDraft)(e)?(0,Te.current)(e):e}function me(e,t,r){e=j(e);let n=Q(r.ids),o=new Set(n),a=[],s=new Set([]),y=[];for(let c of e){let l=_(c,t);o.has(l)||s.has(l)?y.push({id:l,changes:c}):(s.add(l),a.push(c))}return[a,y,n]}function ge(e){function t(p,h){let u=_(p,e);u in h.entities||(h.ids.push(u),h.entities[u]=p)}function r(p,h){p=j(p);for(let u of p)t(u,h)}function n(p,h){let u=_(p,e);u in h.entities||h.ids.push(u),h.entities[u]=p}function o(p,h){p=j(p);for(let u of p)n(u,h)}function a(p,h){p=j(p),h.ids=[],h.entities={},r(p,h)}function s(p,h){return y([p],h)}function y(p,h){let u=!1;p.forEach(f=>{f in h.entities&&(delete h.entities[f],u=!0)}),u&&(h.ids=h.ids.filter(f=>f in h.entities))}function c(p){Object.assign(p,{ids:[],entities:{}})}function l(p,h,u){let f=u.entities[h.id];if(f===void 0)return!1;let k=Object.assign({},f,h.changes),A=_(k,e),m=A!==h.id;return m&&(p[h.id]=A,delete u.entities[h.id]),u.entities[A]=k,m}function i(p,h){return d([p],h)}function d(p,h){let u={},f={};p.forEach(A=>{A.id in h.entities&&(f[A.id]={id:A.id,changes:{...f[A.id]?.changes,...A.changes}})}),p=Object.values(f),p.length>0&&p.filter(m=>l(u,m,h)).length>0&&(h.ids=Object.values(h.entities).map(m=>_(m,e)))}function T(p,h){return g([p],h)}function g(p,h){let[u,f]=me(p,e,h);r(u,h),d(f,h)}return{removeAll:ht(c),addOne:M(t),addMany:M(r),setOne:M(n),setMany:M(o),setAll:M(a),updateOne:M(i),updateMany:M(d),upsertOne:M(T),upsertMany:M(g),removeOne:M(s),removeMany:M(y)}}function nn(e,t,r){let n=0,o=e.length;for(;n<o;){let a=n+o>>>1,s=e[a];r(t,s)>=0?n=a+1:o=a}return n}function rn(e,t,r){let n=nn(e,t,r);return e.splice(n,0,t),e}function At(e,t){let{removeOne:r,removeMany:n,removeAll:o}=ge(e);function a(u,f){return s([u],f)}function s(u,f,k){u=j(u);let A=new Set(k??Q(f.ids)),m=u.filter(S=>!A.has(_(S,e)));m.length!==0&&h(f,m)}function y(u,f){return c([u],f)}function c(u,f){if(u=j(u),u.length!==0){for(let k of u)delete f.entities[e(k)];h(f,u)}}function l(u,f){u=j(u),f.entities={},f.ids=[],s(u,f,[])}function i(u,f){return d([u],f)}function d(u,f){let k=!1,A=!1;for(let m of u){let S=f.entities[m.id];if(!S)continue;k=!0,Object.assign(S,m.changes);let P=e(S);if(m.id!==P){A=!0,delete f.entities[m.id];let E=f.ids.indexOf(m.id);f.ids[E]=P,f.entities[P]=S}}k&&h(f,[],k,A)}function T(u,f){return g([u],f)}function g(u,f){let[k,A,m]=me(u,e,f);k.length&&s(k,f,m),A.length&&d(A,f)}function p(u,f){if(u.length!==f.length)return!1;for(let k=0;k<u.length;k++)if(u[k]!==f[k])return!1;return!0}let h=(u,f,k,A)=>{let m=Q(u.entities),S=Q(u.ids),P=u.entities,E=S;A&&(E=new Set(S));let R=[];for(let z of E){let Xe=m[z];Xe&&R.push(Xe)}let q=R.length===0;for(let z of f)P[e(z)]=z,q||rn(R,z,t);q?R=f.slice().sort(t):k&&R.sort(t);let Z=R.map(e);p(S,Z)||(u.ids=Z)};return{removeOne:r,removeMany:n,removeAll:o,addOne:M(a),updateOne:M(i),upsertOne:M(T),setOne:M(y),setMany:M(c),setAll:M(l),addMany:M(s),updateMany:M(d),upsertMany:M(g)}}function Tt(e={}){let{selectId:t,sortComparer:r}={sortComparer:!1,selectId:s=>s.id,...e},n=r?At(t,r):ge(t),o=ft(n),a=yt();return{selectId:t,sortComparer:r,...o,...a,...n}}var Mt=require("redux");var on="task",mt="listener",gt="completed",Ge="cancelled",St=`task-${Ge}`,kt=`task-${gt}`,Se=`${mt}-${Ge}`,xt=`${mt}-${gt}`,I=class{constructor(t){this.code=t;this.message=`${on} ${Ge} (reason: ${t})`}name="TaskAbortError";message};var ke=(e,t)=>{if(typeof e!="function")throw new TypeError(x(32))},K=()=>{},xe=(e,t=K)=>(e.catch(t),e),Ce=(e,t)=>(e.addEventListener("abort",t,{once:!0}),()=>e.removeEventListener("abort",t)),L=(e,t)=>{let r=e.signal;r.aborted||("reason"in r||Object.defineProperty(r,"reason",{enumerable:!0,value:t,configurable:!0,writable:!0}),e.abort(t))};var U=e=>{if(e.aborted){let{reason:t}=e;throw new I(t)}};function Be(e,t){let r=K;return new Promise((n,o)=>{let a=()=>o(new I(e.reason));if(e.aborted){a();return}r=Ce(e,a),t.finally(()=>r()).then(n,o)}).finally(()=>{r=K})}var Ct=async(e,t)=>{try{return await Promise.resolve(),{status:"ok",value:await e()}}catch(r){return{status:r instanceof I?"cancelled":"rejected",error:r}}finally{t?.()}},Y=e=>t=>xe(Be(e,t).then(r=>(U(e),r))),Ke=e=>{let t=Y(e);return r=>t(new Promise(n=>setTimeout(n,r)))};var{assign:H}=Object,Et={},Ee="listenerMiddleware",an=(e,t)=>{let r=n=>Ce(e,()=>L(n,e.reason));return(n,o)=>{ke(n,"taskExecutor");let a=new AbortController;r(a);let s=Ct(async()=>{U(e),U(a.signal);let y=await n({pause:Y(a.signal),delay:Ke(a.signal),signal:a.signal});return U(a.signal),y},()=>L(a,kt));return o?.autoJoin&&t.push(s.catch(K)),{result:Y(e)(s),cancel(){L(a,St)}}}},sn=(e,t)=>{let r=async(n,o)=>{U(t);let a=()=>{},y=[new Promise((c,l)=>{let i=e({predicate:n,effect:(d,T)=>{T.unsubscribe(),c([d,T.getState(),T.getOriginalState()])}});a=()=>{i(),l()}})];o!=null&&y.push(new Promise(c=>setTimeout(c,o,null)));try{let c=await Be(t,Promise.race(y));return U(t),c}finally{a()}};return(n,o)=>xe(r(n,o))},Pt=e=>{let{type:t,actionCreator:r,matcher:n,predicate:o,effect:a}=e;if(t)o=b(t).match;else if(r)t=r.type,o=r.match;else if(n)o=n;else if(!o)throw new Error(x(21));return ke(a,"options.listener"),{predicate:o,type:t,effect:a}},bt=H(e=>{let{type:t,predicate:r,effect:n}=Pt(e);return{id:O(),effect:n,type:t,predicate:r,pending:new Set,unsubscribe:()=>{throw new Error(x(22))}}},{withTypes:()=>bt}),Rt=(e,t)=>{let{type:r,effect:n,predicate:o}=Pt(t);return Array.from(e.values()).find(a=>(typeof r=="string"?a.type===r:a.predicate===o)&&a.effect===n)},He=e=>{e.pending.forEach(t=>{L(t,Se)})},cn=e=>()=>{e.forEach(He),e.clear()},wt=(e,t,r)=>{try{e(t,r)}catch(n){setTimeout(()=>{throw n},0)}},Re=H(b(`${Ee}/add`),{withTypes:()=>Re}),qe=b(`${Ee}/removeAll`),we=H(b(`${Ee}/remove`),{withTypes:()=>we}),dn=(...e)=>{console.error(`${Ee}/error`,...e)},It=(e={})=>{let t=new Map,{extra:r,onError:n=dn}=e;ke(n,"onError");let o=i=>(i.unsubscribe=()=>t.delete(i.id),t.set(i.id,i),d=>{i.unsubscribe(),d?.cancelActive&&He(i)}),a=i=>{let d=Rt(t,i)??bt(i);return o(d)};H(a,{withTypes:()=>a});let s=i=>{let d=Rt(t,i);return d&&(d.unsubscribe(),i.cancelActive&&He(d)),!!d};H(s,{withTypes:()=>s});let y=async(i,d,T,g)=>{let p=new AbortController,h=sn(a,p.signal),u=[];try{i.pending.add(p),await Promise.resolve(i.effect(d,H({},T,{getOriginalState:g,condition:(f,k)=>h(f,k).then(Boolean),take:h,delay:Ke(p.signal),pause:Y(p.signal),extra:r,signal:p.signal,fork:an(p.signal,u),unsubscribe:i.unsubscribe,subscribe:()=>{t.set(i.id,i)},cancelActiveListeners:()=>{i.pending.forEach((f,k,A)=>{f!==p&&(L(f,Se),A.delete(f))})},cancel:()=>{L(p,Se),i.pending.delete(p)},throwIfCancelled:()=>{U(p.signal)}})))}catch(f){f instanceof I||wt(n,f,{raisedBy:"effect"})}finally{await Promise.all(u),L(p,xt),i.pending.delete(p)}},c=cn(t);return{middleware:i=>d=>T=>{if(!(0,Mt.isAction)(T))return d(T);if(Re.match(T))return a(T.payload);if(qe.match(T)){c();return}if(we.match(T))return s(T.payload);let g=i.getState(),p=()=>{if(g===Et)throw new Error(x(23));return g},h;try{if(h=d(T),t.size>0){let u=i.getState(),f=Array.from(t.values());for(let k of f){let A=!1;try{A=k.predicate(T,u,g)}catch(m){A=!1,wt(n,m,{raisedBy:"predicate"})}A&&y(k,T,i,p)}}}finally{g=Et}return h},startListening:a,stopListening:s,clearListeners:c}};var vt=require("redux");var un=e=>({middleware:e,applied:new Map}),ln=e=>t=>t?.meta?.instanceId===e,Dt=()=>{let e=O(),t=new Map,r=Object.assign(b("dynamicMiddleware/add",(...y)=>({payload:y,meta:{instanceId:e}})),{withTypes:()=>r}),n=Object.assign(function(...c){c.forEach(l=>{v(t,l,un)})},{withTypes:()=>n}),o=y=>{let c=Array.from(t.values()).map(l=>v(l.applied,y,l.middleware));return(0,vt.compose)(...c)},a=B(r,ln(e));return{middleware:y=>c=>l=>a(l)?(n(...l.payload),y.dispatch):o(y)(c)(l),addMiddleware:n,withMiddleware:r,instanceId:e}};var Ot=require("redux");var pn=e=>"reducerPath"in e&&typeof e.reducerPath=="string",fn=e=>e.flatMap(t=>pn(t)?[[t.reducerPath,t.reducer]]:Object.entries(t)),$e=Symbol.for("rtk-state-proxy-original"),yn=e=>!!e&&!!e[$e],hn=new WeakMap,An=(e,t,r)=>v(hn,e,()=>new Proxy(e,{get:(n,o,a)=>{if(o===$e)return n;let s=Reflect.get(n,o,a);if(typeof s>"u"){let y=r[o];if(typeof y<"u")return y;let c=t[o];if(c){let l=c(void 0,{type:O()});if(typeof l>"u")throw new Error(x(24));return r[o]=l,l}}return s}})),Tn=e=>{if(!yn(e))throw new Error(x(25));return e[$e]},mn={},gn=(e=mn)=>e;function Nt(...e){let t=Object.fromEntries(fn(e)),r=()=>Object.keys(t).length?(0,Ot.combineReducers)(t):gn,n=r();function o(c,l){return n(c,l)}o.withLazyLoadedSlices=()=>o;let a={},s=(c,l={})=>{let{reducerPath:i,reducer:d}=c,T=t[i];return!l.overrideExisting&&T&&T!==d?(typeof process<"u",o):(l.overrideExisting&&T!==d&&delete a[i],t[i]=d,n=r(),o)},y=Object.assign(function(l,i){return function(T,...g){return l(An(i?i(T,...g):T,t,a),...g)}},{original:Tn});return Object.assign(o,{inject:s,selector:y})}function x(e){return`Minified Redux Toolkit error #${e}; visit https://redux-toolkit.js.org/Errors?code=${e} for the full message or use the non-minified dev environment for full errors. `}0&&(module.exports={ReducerType,SHOULD_AUTOBATCH,TaskAbortError,Tuple,addListener,asyncThunkCreator,autoBatchEnhancer,buildCreateSlice,clearAllListeners,combineSlices,configureStore,createAction,createActionCreatorInvariantMiddleware,createAsyncThunk,createDraftSafeSelector,createDraftSafeSelectorCreator,createDynamicMiddleware,createEntityAdapter,createImmutableStateInvariantMiddleware,createListenerMiddleware,createNextState,createReducer,createSelector,createSelectorCreator,createSerializableStateInvariantMiddleware,createSlice,current,findNonSerializableValue,formatProdErrorMessage,freeze,isActionCreator,isAllOf,isAnyOf,isAsyncThunkAction,isDraft,isFluxStandardAction,isFulfilled,isImmutableDefault,isPending,isPlain,isRejected,isRejectedWithValue,lruMemoize,miniSerializeError,nanoid,original,prepareAutoBatched,removeListener,unwrapResult,weakMapMemoize,...require("redux")});
//# sourceMappingURL=redux-toolkit.production.min.cjs.map