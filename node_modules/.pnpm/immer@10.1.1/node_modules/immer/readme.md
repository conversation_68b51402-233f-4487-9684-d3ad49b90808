<img src="images/immer-logo.svg" height="200px" align="right"/>

# Immer

[![npm](https://img.shields.io/npm/v/immer.svg)](https://www.npmjs.com/package/immer) [![Build Status](https://github.com/immerjs/immer/actions/workflows/test.yml/badge.svg?branch=main)](https://github.com/immerjs/immer/actions?query=branch%3Amain) [![Coverage Status](https://coveralls.io/repos/github/immerjs/immer/badge.svg?branch=main)](https://coveralls.io/github/immerjs/immer?branch=main) [![code style: prettier](https://img.shields.io/badge/code_style-prettier-ff69b4.svg)](https://github.com/prettier/prettier) [![OpenCollective](https://opencollective.com/immer/backers/badge.svg)](#backers) [![OpenCollective](https://opencollective.com/immer/sponsors/badge.svg)](#sponsors) [![Gitpod Ready-to-Code](https://img.shields.io/badge/Gitpod-Ready--to--Code-blue?logo=gitpod)](https://gitpod.io/#https://github.com/immerjs/immer)

_Create the next immutable state tree by simply modifying the current tree_

Winner of the "Breakthrough of the year" [React open source award](https://osawards.com/react/) and "Most impactful contribution" [JavaScript open source award](https://osawards.com/javascript/) in 2019

## Contribute using one-click online setup

You can use Gitpod (a free online VS Code like IDE) for contributing online. With a single click it will launch a workspace and automatically:

- clone the immer repo.
- install the dependencies.
- run `yarn run start`.

so that you can start coding straight away.

[![Open in Gitpod](https://gitpod.io/button/open-in-gitpod.svg)](https://gitpod.io/from-referrer/)

## Documentation

The documentation of this package is hosted at https://immerjs.github.io/immer/

## Support

Did Immer make a difference to your project? Join the open collective at https://opencollective.com/immer!

## Release notes

https://github.com/immerjs/immer/releases
