hoistPattern:
  - '*'
hoistedDependencies:
  '@standard-schema/spec@1.0.0':
    '@standard-schema/spec': private
  '@standard-schema/utils@0.3.0':
    '@standard-schema/utils': private
  '@types/use-sync-external-store@0.0.6':
    '@types/use-sync-external-store': private
  immer@10.1.1:
    immer: private
  react@19.1.0:
    react: private
  redux-thunk@3.1.0(redux@5.0.1):
    redux-thunk: private
  redux@5.0.1:
    redux: private
  reselect@5.1.1:
    reselect: private
  use-sync-external-store@1.5.0(react@19.1.0):
    use-sync-external-store: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.8.1
pendingBuilds: []
prunedAt: Wed, 23 Jul 2025 06:02:44 GMT
publicHoistPattern: []
registries:
  default: https://registry.npmmirror.com/
skipped: []
storeDir: /Users/<USER>/Library/pnpm/store/v10
virtualStoreDir: .pnpm
virtualStoreDirMaxLength: 120
