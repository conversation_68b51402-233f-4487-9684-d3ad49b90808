import { Layout, Menu } from 'antd';
import { Outlet } from 'react-router-dom';

const { Header, Content, Sider } = Layout;

function App() {
  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Sider>
        <div style={{ height: 32, margin: 16, background: 'rgba(255, 255, 255, 0.2)' }} />
        <Menu
          theme="dark"
          mode="inline"
          defaultSelectedKeys={['1']}
          items={[{ key: '1', label: 'Dashboard' }]}
        />
      </Sider>
      <Layout>
        <Header style={{ padding: 0, background: '#fff' }} />
        <Content style={{ margin: '24px 16px 0' }}>
          <div style={{ padding: 24, minHeight: 360, background: '#fff' }}>
            {/* 子路由会在这里渲染 */}
            <Outlet />
          </div>
        </Content>
      </Layout>
    </Layout>
  );
}

export default App;
