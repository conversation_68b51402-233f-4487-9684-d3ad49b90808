import { createSlice, type PayloadAction } from '@reduxjs/toolkit';

interface UserState {
  token: string | null;
  userInfo: Record<string, unknown> | null;
}

const initialState: UserState = {
  token: localStorage.getItem('token'),
  userInfo: null,
};

const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    setToken(state, action: PayloadAction<string>) {
      state.token = action.payload;
      localStorage.setItem('token', action.payload);
    },
    setUserInfo(state, action: PayloadAction<Record<string, any>>) {
      state.userInfo = action.payload;
    },
  },
});

export const { setToken, setUserInfo } = userSlice.actions;
export default userSlice.reducer;